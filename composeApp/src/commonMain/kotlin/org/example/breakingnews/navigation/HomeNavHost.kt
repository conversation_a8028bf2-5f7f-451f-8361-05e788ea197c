package org.example.breakingnews.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHost
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController


@Composable
fun HomeNavHost(
    modifier: Modifier = Modifier,
    navHostController: NavHostController,
) {
    val navController = rememberNavController()
    NavHost(

    )
}