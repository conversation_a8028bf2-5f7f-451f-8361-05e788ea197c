package org.example.breakingnews.features
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.*
import androidx.compose.material3.*
import androidx.compose.ui.unit.dp
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.example.breakingnews.Platform
import androidx.compose.foundation.lazy.*

@Preview
@Composable
fun AboutScreen() {
    LazyColumn(modifier = Modifier.fillMaxSize()) {
        items(makeItems()) { item ->
            AboutComponent(
                title = item.first,
                subtitle = item.second
            )
        }
    }
}

@Composable
private fun AboutComponent(
    title: String,
    subtitle: String,
) {
    Column(modifier = Modifier.padding(16.dp)) {
        Text(text=title)
        Spacer(modifier = Modifier.height(16.dp))
        Text(text=subtitle)
    }
    HorizontalDivider()
}


//@Composable
//fun AboutScreenPreview() {
//    AboutComponent("Thiago", "Araujo")
//}

private fun makeItems(): List<Pair<String, String>> {
    val platform = Platform()
    return listOf(
        Pair("OS Name", platform.osName),
        Pair("OS Version", platform.osVersion ?: "N/A"),
        Pair("Device Model", platform.deviceModel ?: "N/A"),
    )

}